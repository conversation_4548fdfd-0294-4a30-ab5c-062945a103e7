.medpro-all-blog-wrapper {
  min-height: 100vh;
  background-color: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; /* Reverted to original font stack */
}

/* Header Styles */
.medpro-all-blog-header {
  background: #0ea5e9;
  padding: 32px 0 16px 0;
  text-align: center;
  margin-top: -40px; /* Adjusted for fixed header */
}

.medpro-all-blog-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.medpro-all-blog-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.medpro-all-blog-logo {
  text-decoration: none;
}

.medpro-all-blog-logo-text {
  color: white;
  font-size: 32px;
  font-weight: bold;
  letter-spacing: 2px;
}

.medpro-all-blog-main-nav {
  display: flex;
  gap: 32px;
}

.medpro-all-blog-main-nav a {
  margin-right: 18px;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s;
  color: white;
}

/* Search and Filter Styles */
.blog-search-container {
  margin-bottom: 24px;
}

.blog-search-container .ant-input-search {
  border-radius: 12px !important;
  overflow: hidden;
}

.blog-search-container .ant-input-search .ant-input {
  border-radius: 12px 0 0 12px !important;
  border: 2px solid #e1e5e9;
  padding: 12px 16px;
  font-size: 16px;
  transition: all 0.3s ease;
}

.blog-search-container .ant-input-search .ant-input:focus {
  border-color: #0ea5e9;
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.blog-search-container .ant-input-search .ant-input-search-button {
  border-radius: 0 12px 12px 0 !important;
  background: linear-gradient(135deg, #0ea5e9, #0284c7);
  border: 2px solid #0ea5e9;
  height: auto;
  padding: 12px 24px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.blog-search-container .ant-input-search .ant-input-search-button:hover {
  background: linear-gradient(135deg, #0284c7, #0369a1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
}

.blog-tag-filter-group {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #e1e5e9;
}

.blog-tag-filter-group .ant-select {
  border-radius: 8px;
}

.blog-tag-filter-group .ant-select .ant-select-selector {
  border-radius: 8px !important;
  border: 2px solid #e1e5e9 !important;
  transition: all 0.3s ease;
}

.blog-tag-filter-group .ant-select.ant-select-focused .ant-select-selector {
  border-color: #0ea5e9 !important;
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1) !important;
}

/* Responsive Design for Search and Filter */
@media (max-width: 768px) {
  .blog-tag-filter-group {
    flex-direction: column;
    gap: 12px !important;
    align-items: stretch !important;
  }

  .blog-tag-filter-group span {
    text-align: center;
    min-width: auto !important;
  }

  .blog-search-container {
    padding: 0 16px !important;
  }
}

.medpro-all-blog-main-nav a:hover {
  color: #1e40af;
}

/* Main Layout */
.medpro-all-blog-main {
  padding: 32px 0;
}

/* Featured Section with Sidebar */
.medpro-all-blog-featured-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 32px;
  margin-bottom: 48px;
}

.medpro-all-blog-featured-content-wrapper {
  min-width: 0;
}

.medpro-all-blog-featured-article {
  background: #e0f2fe;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 32px;
}

.medpro-all-blog-featured-background {
  position: relative;
  height: 320px;
  overflow: hidden;
}

.medpro-all-blog-featured-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.medpro-all-blog-featured-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.8) 0%,
    rgba(147, 51, 234, 0.6) 100%
  );
  display: flex;
  align-items: center;
  padding: 32px;
}

.medpro-all-blog-featured-content {
  color: white;
  max-width: 500px;
}

.medpro-all-blog-featured-title {
  font-size: 28px;
  font-weight: bold;
  line-height: 1.2;
  margin: 0 0 16px 0;
}

.medpro-all-blog-hotline-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.medpro-all-blog-featured-text-content {
  padding: 24px;
}

.medpro-all-blog-featured-subtitle {
  font-size: 24px;
  font-weight: bold;
  color: #1e293b;
  margin: 0 0 16px 0;
  line-height: 1.3;
}

.medpro-all-blog-featured-excerpt {
  color: #64748b;
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 16px 0;
}

.medpro-all-blog-featured-meta {
  margin-bottom: 16px;
}

.medpro-all-blog-featured-date {
  color: #94a3b8;
  font-size: 14px;
}

.medpro-all-blog-read-more-link {
  color: #06b6d4;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
}

.medpro-all-blog-read-more-link:hover {
  color: #0891b2;
}

/* Bottom Featured Cards */
.medpro-all-blog-bottom-featured-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.medpro-all-blog-bottom-card {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.medpro-all-blog-bottom-card-image {
  position: relative;
  height: 160px;
}

.medpro-all-blog-card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.medpro-all-blog-card-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.5) 100%
  );
  color: white;
  padding: 7px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.medpro-all-blog-card-title {
  font-size: 18px;
  font-weight: bold;
  margin: 0 0 2px 0;
  margin-top: 20px;
}

.medpro-all-blog-card-subtitle {
  font-size: 14px;
  font-weight: 400;
  margin: 0 0 6px 0;
  line-height: 1.3;
}

.medpro-all-blog-card-description {
  font-size: 12px;
  line-height: 1.4;
  margin: 0 0 10px 0;
  opacity: 0.9;
}

.medpro-all-blog-card-link {
  color: #06b6d4;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
}

/* Sidebar */
.medpro-all-blog-sidebar {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.medpro-all-blog-sidebar-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.medpro-all-blog-sidebar-article {
  display: flex;
  gap: 12px;
  padding: 18px;
  background: #e0f2fe;
  border-radius: 8px;
  text-decoration: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.medpro-all-blog-sidebar-article:hover {
  transform: translateY(-1px);
  background: #cceeff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.medpro-all-blog-sidebar-image {
  flex-shrink: 0;
}

.medpro-all-blog-sidebar-article-image {
  border-radius: 6px;
  object-fit: cover;
  width: 80px;
  height: 60px;
}

.medpro-all-blog-sidebar-text {
  flex: 1;
  min-width: 0;
}

.medpro-all-blog-sidebar-category {
  color: #06b6d4;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  display: block;
  margin-bottom: 4px;
}

.medpro-all-blog-sidebar-title {
  font-size: 15px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.medpro-all-blog-sidebar-date {
  color: #94a3b8;
  font-size: 11px;
}

/* Main Sections */
.medpro-all-blog-main-sections {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Service Section */
.medpro-all-blog-service-section {
  margin-bottom: 48px;
}

.medpro-all-blog-section-title {
  font-size: 32px;
  font-weight: 700;
  color: #0ea5e9;
  margin: 40px 0 32px 0;
  position: relative;
  text-align: left;
  padding-left: 20px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.medpro-all-blog-section-title::after {
  content: "";
  position: absolute;
  bottom: -8px;
  left: 20px;
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #0ea5e9, #06b6d4);
  border-radius: 2px;
}

/* Consistent spacing for all sections */
.medpro-all-blog-medical-section,
.medpro-all-blog-service-section,
.medpro-all-blog-medical-knowledge-section {
  margin: 60px 0;
  padding: 0;
}

/* Section titles alignment - all same */
.medpro-all-blog-section-title {
  margin-left: 0 !important;
  margin-right: 0 !important;
  padding-left: 0 !important;
}

/* First section title (Tin Y Tế) - same as others */
.medpro-all-blog-main > .medpro-all-blog-section-title:first-child {
  margin: 20px 0 40px 0 !important;
  max-width: 1200px;
  margin-left: auto !important;
  margin-right: auto !important;
  padding: 0 20px;
}

/* New Layout Styles */
.medpro-all-blog-section-header {
  margin-top: 0;
  margin-bottom: 24px;
  display: block;
  text-align: left;
}

.medpro-all-blog-view-more-link {
  color: #0ea5e9;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  transition: color 0.3s ease;
}

.medpro-all-blog-view-more-link:hover {
  color: #0284c7;
  text-decoration: underline;
}

.medpro-all-blog-two-column-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32px;
  padding: 0 20px;
}

.medpro-all-blog-preview-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.medpro-all-blog-preview-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.medpro-all-blog-preview-card-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.medpro-all-blog-preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.medpro-all-blog-preview-card:hover .medpro-all-blog-preview-image {
  transform: scale(1.05);
}

.medpro-all-blog-preview-card-content {
  padding: 20px;
}

.medpro-all-blog-preview-category {
  display: inline-block;
  background: #e0f2fe;
  color: #0ea5e9;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 12px;
}

.medpro-all-blog-preview-title {
  font-size: 18px;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 12px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.medpro-all-blog-preview-excerpt {
  color: #64748b;
  font-size: 14px;
  line-height: 1.6;
  margin: 0 0 16px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.medpro-all-blog-preview-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  font-size: 12px;
  color: #94a3b8;
}

.medpro-all-blog-preview-date {
  font-weight: 500;
}

.medpro-all-blog-preview-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.medpro-all-blog-preview-stats .stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #666;
  font-size: 0.85rem;
  font-weight: 500;
}

.medpro-all-blog-preview-stats .stat-icon {
  font-size: 0.9rem;
}

.medpro-all-blog-preview-stats .stat-count {
  font-weight: 600;
  color: #333;
}

.medpro-all-blog-preview-stats .like-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px 6px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.medpro-all-blog-preview-stats .like-button:hover {
  background-color: #ffe6e6;
  transform: scale(1.05);
}

.medpro-all-blog-preview-stats .like-button:active {
  transform: scale(0.95);
}

.medpro-all-blog-preview-stats .like-button.liking {
  opacity: 0.6;
  cursor: not-allowed;
}

.medpro-all-blog-preview-stats .like-button.liking .stat-icon {
  animation: heartbeat 0.6s ease-in-out;
}

@keyframes heartbeat {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.medpro-all-blog-preview-link {
  color: #0ea5e9;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  transition: color 0.3s ease;
}

.medpro-all-blog-preview-link:hover {
  color: #0284c7;
  text-decoration: underline;
}

.medpro-all-blog-service-slider {
  position: relative;
  margin-bottom: 32px;
}

.medpro-all-blog-service-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.medpro-all-blog-service-card {
  background: #e0f2fe;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.medpro-all-blog-service-card:hover {
  transform: translateY(-2px);
}

.medpro-all-blog-service-card-image {
  height: 140px;
  overflow: hidden;
}

.medpro-all-blog-service-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.medpro-all-blog-service-card-content {
  padding: 14px;
}

.medpro-all-blog-service-category {
  color: #f59e0b;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.medpro-all-blog-service-title {
  font-size: 13px;
  font-weight: 600;
  color: #1e293b;
  margin: 6px 0;
  line-height: 1.3;
}

.medpro-all-blog-service-date {
  color: #94a3b8;
  font-size: 12px;
  display: block;
  margin-bottom: 8px;
}

.medpro-all-blog-service-link {
  color: #06b6d4;
  text-decoration: none;
  font-size: 12px;
  font-weight: 500;
}

.medpro-all-blog-slider-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.medpro-all-blog-slider-btn:hover {
  background: #f8fafc;
}

.medpro-all-blog-slider-btn.medpro-all-blog-prev {
  left: -20px;
}

.medpro-all-blog-slider-btn.medpro-all-blog-next {
  right: -20px;
}

/* Medical Knowledge Section */
.medpro-all-blog-medical-knowledge-section {
  margin-bottom: 48px;
}

.medpro-all-blog-knowledge-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
  margin-bottom: 32px;
}

.medpro-all-blog-knowledge-featured {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.medpro-all-blog-knowledge-featured-image {
  position: relative;
  height: 320px;
}

.medpro-all-blog-knowledge-bg-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.medpro-all-blog-knowledge-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.5) 100%
  );
  color: white;
  padding: 24px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.medpro-all-blog-knowledge-category {
  color: #06b6d4;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  margin-bottom: 8px;
}

.medpro-all-blog-knowledge-featured-title {
  font-size: 18px;
  font-weight: bold;
  margin: 0 0 10px 0;
  line-height: 1.3;
}

.medpro-all-blog-knowledge-featured-excerpt {
  font-size: 13px;
  line-height: 1.4;
  margin: 0 0 14px 0;
  opacity: 0.9;
}

.medpro-all-blog-knowledge-link {
  color: #06b6d4;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
}

/* Knowledge Cards */
.medpro-all-blog-knowledge-cards {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
}

.medpro-all-blog-knowledge-card {
  background: #e0f2fe;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.medpro-all-blog-knowledge-card:hover {
  transform: translateY(-2px);
}

.medpro-all-blog-knowledge-card-category {
  color: #06b6d4;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  display: block;
  margin-bottom: 8px;
}

.medpro-all-blog-knowledge-card-title {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 10px 0;
  line-height: 1.3;
}

.medpro-all-blog-knowledge-card-excerpt {
  color: #64748b;
  font-size: 13px;
  line-height: 1.5;
  margin: 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* View All Button */
.medpro-all-blog-view-all-section {
  text-align: center;
}

.medpro-all-blog-view-all-btn {
  background: #06b6d4;
  color: white;
  text-decoration: none;
  padding: 12px 32px;
  border-radius: 25px;
  font-weight: 500;
  display: inline-block;
  transition: background-color 0.2s;
}

.medpro-all-blog-view-all-btn:hover {
  background: #0891b2;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .medpro-all-blog-featured-section {
    grid-template-columns: 1fr;
    gap: 32px;
  }

  .medpro-all-blog-bottom-featured-cards,
  .medpro-all-blog-service-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .medpro-all-blog-knowledge-layout {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .medpro-all-blog-header-content {
    flex-direction: column;
    height: auto;
    padding: 16px 0;
    gap: 16px;
  }

  .medpro-all-blog-main-nav {
    gap: 20px;
  }

  .medpro-all-blog-featured-title {
    font-size: 28px;
  }

  .medpro-all-blog-bottom-featured-cards,
  .medpro-all-blog-service-cards {
    grid-template-columns: 1fr;
  }

  .medpro-all-blog-container {
    padding: 0 16px;
  }

  .medpro-all-blog-slider-btn {
    display: none;
  }
}

@media (max-width: 480px) {
  .medpro-all-blog-main-nav {
    flex-wrap: wrap;
    justify-content: center;
    gap: 16px;
  }

  .medpro-all-blog-featured-overlay {
    padding: 20px;
  }

  .medpro-all-blog-featured-title {
    font-size: 24px;
  }

  .medpro-all-blog-featured-text-content {
    padding: 20px;
  }
}

/* Page Title Section */
.medpro-all-blog-page-title-section {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  padding: 40px 0;
  margin-bottom: 40px;
}

.medpro-all-blog-page-title {
  font-size: 48px;
  font-weight: 700;
  color: white;
  text-align: center;
  margin: 0 0 12px 0;
  letter-spacing: 2px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.medpro-all-blog-page-subtitle {
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  margin: 0;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

/* Responsive for Page Title */
@media (max-width: 768px) {
  .medpro-all-blog-page-title-section {
    padding: 30px 0;
    margin-bottom: 30px;
  }

  .medpro-all-blog-page-title {
    font-size: 36px;
    letter-spacing: 1px;
  }

  .medpro-all-blog-page-subtitle {
    font-size: 16px;
    padding: 0 20px;
  }
}

@media (max-width: 480px) {
  .medpro-all-blog-page-title {
    font-size: 28px;
  }

  .medpro-all-blog-page-subtitle {
    font-size: 14px;
  }
}

/* Medical Section Styles */
.medpro-all-blog-medical-section {
  margin: 40px 0;
  padding: 0 20px;
}

.medpro-all-blog-medical-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
  margin-top: 24px;
}

.medpro-all-blog-medical-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
}

.medpro-all-blog-medical-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.medpro-all-blog-medical-card-image {
  position: relative;
  overflow: hidden;
  height: 200px;
}

.medpro-all-blog-medical-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.medpro-all-blog-medical-card:hover .medpro-all-blog-medical-image {
  transform: scale(1.05);
}

.medpro-all-blog-medical-card-content {
  padding: 20px;
}

.medpro-all-blog-medical-category {
  background: #e0f2fe;
  color: #0277bd;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.medpro-all-blog-medical-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 12px 0 8px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.medpro-all-blog-medical-excerpt {
  color: #64748b;
  font-size: 14px;
  line-height: 1.6;
  margin: 8px 0 12px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.medpro-all-blog-medical-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 12px 0;
  padding-top: 12px;
  border-top: 1px solid #e2e8f0;
}

.medpro-all-blog-medical-date {
  color: #64748b;
  font-size: 12px;
}

.medpro-all-blog-medical-stats {
  color: #64748b;
  font-size: 12px;
  display: flex;
  gap: 8px;
}

.medpro-all-blog-medical-link {
  display: inline-flex;
  align-items: center;
  color: #0ea5e9;
  text-decoration: none;
  font-weight: 500;
  font-size: 14px;
  transition: color 0.2s;
}

.medpro-all-blog-medical-link:hover {
  color: #0284c7;
}

/* Responsive for Medical Section */
@media (max-width: 768px) {
  .medpro-all-blog-medical-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .medpro-all-blog-medical-card-content {
    padding: 16px;
  }

  .medpro-all-blog-medical-title {
    font-size: 16px;
  }

  /* New responsive styles for two-column layout */
  .medpro-all-blog-two-column-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 0 16px;
  }

  .medpro-all-blog-section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
    padding: 0 16px;
  }

  .medpro-all-blog-preview-card-content {
    padding: 16px;
  }

  .medpro-all-blog-preview-title {
    font-size: 16px;
  }
}

/* Service Blogs Grid - fixed 3 columns, no center, no max-width on card */
.medpro-all-blog-service-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32px;
  max-width: 1200px;
  margin: 0 auto;
}

.service-blog-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 4px 24px rgba(56, 189, 248, 0.08);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 0 0 24px 0;
  margin-bottom: 0;
  transition: box-shadow 0.2s;
}

.service-blog-image {
  width: 100%;
  height: 180px;
  object-fit: cover;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  margin-bottom: 0;
  display: block;
}

.service-blog-content {
  padding: 24px;
  display: flex;
  flex-direction: column;
  flex: 1;
  background: transparent;
}

.service-blog-tag {
  color: #f59e0b;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  display: inline-block;
}

.service-blog-title {
  font-size: 1.2rem;
  font-weight: 700;
  color: #0f3a5b;
  margin: 0 0 10px 0;
  line-height: 1.4;
  word-break: break-word;
}

.service-blog-desc {
  color: #64748b;
  font-size: 15px;
  margin-bottom: 16px;
  flex: 1;
}

.service-blog-meta {
  font-size: 13px;
  color: #94a3b8;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

.service-blog-meta .icon-calendar {
  margin-right: 4px;
  color: #0283f5;
}

.service-blog-link {
  color: #0283f5;
  font-weight: 600;
  text-decoration: none;
  font-size: 15px;
  transition: color 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.service-blog-link:hover {
  color: #0369a1;
  text-decoration: underline;
}

@media (max-width: 900px) {
  .medpro-all-blog-service-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

.blog-tag-filter-group {
  display: flex;
  gap: 16px;
  margin-bottom: 18px;
  flex-wrap: wrap;
  background: #f1f5f9;
  border-radius: 12px;
  padding: 12px 18px;
  box-shadow: 0 2px 12px rgba(56, 189, 248, 0.06);
  align-items: center;
  justify-content: center;
}

.blog-tag-filter-btn {
  background: #fff;
  color: #64748b;
  border: 1.5px solid #e0e7ef;
  border-radius: 8px;
  padding: 8px 22px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.18s;
  outline: none;
  box-shadow: 0 1px 4px rgba(56, 189, 248, 0.04);
  margin-bottom: 4px;
  text-transform: capitalize;
  letter-spacing: 0.01em;
}

.blog-tag-filter-btn.active,
.blog-tag-filter-btn:hover {
  background: #e0f2fe;
  color: #0283f5;
  border-color: #38bdf8;
  box-shadow: 0 2px 8px rgba(56, 189, 248, 0.1);
}

@media (max-width: 600px) {
  .blog-tag-filter-group {
    gap: 8px;
    padding: 8px 6px;
    flex-wrap: nowrap;
    overflow-x: auto;
    justify-content: flex-start;
  }
  .blog-tag-filter-btn {
    font-size: 14px;
    padding: 7px 12px;
    white-space: nowrap;
  }
}

.medpro-all-blog-section-description {
  color: #0283f5;
  font-weight: 600;
  font-size: 1.08rem;
  background: linear-gradient(90deg, #38bdf8 0%, #0ea5e9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  margin-top: 0;
  margin-bottom: 0;
}

.breadcrumb {
  margin-bottom: 0 !important;
}

.medpro-all-blog-section-title {
  margin-top: 0 !important;
}
