import api from "../configs/api";

/**
 * Config API functions for system configuration management
 */

// Tạo cấu hình mới
export const createConfig = (configData) => {
  return api.post("/api/config", configData);
};

// L<PERSON>y tất cả cấu hình
export const fetchAllConfigs = () => {
  return api.get("/api/config");
};

// Cập nhật cấu hình theo ID
export const updateConfig = (id, configData) => {
  return api.put(`/api/config/${id}`, configData);
};

// <PERSON><PERSON>a cấu hình theo ID
export const deleteConfig = (id) => {
  return api.delete(`/api/config/${id}`);
};

// L<PERSON>y cấu hình theo ID
export const fetchConfigById = (id) => {
  return api.get(`/api/config/${id}`);
};
